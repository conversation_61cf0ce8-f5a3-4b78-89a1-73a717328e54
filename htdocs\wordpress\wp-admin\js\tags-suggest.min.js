/*! This file is auto-generated */
!function(s){var u=0,n=wp.i18n._x(",","tag delimiter")||",",t=wp.i18n.__,d=wp.i18n._n,l=wp.i18n.sprintf;function p(e){return e.split(new RegExp(n+"\\s*"))}s.fn.wpTagsSuggest=function(e){var i,o,a,r=s(this);return r.length&&(a=(e=e||{}).taxonomy||r.attr("data-wp-taxonomy")||"post_tag",delete e.taxonomy,e=s.extend({source:function(e,n){var t;o===e.term?n(i):(t=p(e.term).pop(),s.get(window.ajaxurl,{action:"ajax-tag-search",tax:a,q:t,number:20}).always(function(){r.removeClass("ui-autocomplete-loading")}).done(function(e){var t,o=[];if(e){for(t in e=e.split("\n")){var a=++u;o.push({id:a,name:e[t]})}n(i=o)}else n(o)}),o=e.term)},focus:function(e,t){r.attr("aria-activedescendant","wp-tags-autocomplete-"+t.item.id),e.preventDefault()},select:function(e,t){var o=p(r.val());return o.pop(),o.push(t.item.name,""),r.val(o.join(n+" ")),s.ui.keyCode.TAB===e.keyCode?(window.wp.a11y.speak(wp.i18n.__("Term selected."),"assertive"),e.preventDefault()):s.ui.keyCode.ENTER===e.keyCode&&(window.tagBox&&(window.tagBox.userAction="add",window.tagBox.flushTags(s(this).closest(".tagsdiv"))),e.preventDefault(),e.stopPropagation()),!1},open:function(){r.attr("aria-expanded","true")},close:function(){r.attr("aria-expanded","false")},minLength:2,position:{my:"left top+2",at:"left bottom",collision:"none"},messages:{noResults:t("No results found."),results:function(e){return l(d("%d result found. Use up and down arrow keys to navigate.","%d results found. Use up and down arrow keys to navigate.",e),e)}}},e),r.on("keydown",function(){r.removeAttr("aria-activedescendant")}),r.autocomplete(e),r.autocomplete("instance"))&&(r.autocomplete("instance")._renderItem=function(e,t){return s('<li role="option" id="wp-tags-autocomplete-'+t.id+'">').text(t.name).appendTo(e)},r.attr({role:"combobox","aria-autocomplete":"list","aria-expanded":"false","aria-owns":r.autocomplete("widget").attr("id")}).on("focus",function(){p(r.val()).pop()&&r.autocomplete("search")}),r.autocomplete("widget").addClass("wp-tags-autocomplete").attr("role","listbox").removeAttr("tabindex").on("menufocus",function(e,t){t.item.attr("aria-selected","true")}).on("menublur",function(){s(this).find('[aria-selected="true"]').removeAttr("aria-selected")})),this}}(jQuery);