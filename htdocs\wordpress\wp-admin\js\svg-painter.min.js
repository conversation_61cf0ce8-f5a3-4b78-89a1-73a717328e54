/*! This file is auto-generated */
window.wp=window.wp||{},wp.svgPainter=function(a,i){"use strict";var n,t,s={},o=[];return a(function(){wp.svgPainter.init()}),{init:function(){t=this,n=a("#adminmenu .wp-menu-image, #wpadminbar .ab-item"),t.setColors(),t.findElements(),t.paint()},setColors:function(n){(n=void 0===n&&void 0!==i._wpColorScheme?i._wpColorScheme:n)&&n.icons&&n.icons.base&&n.icons.current&&n.icons.focus&&(s=n.icons)},findElements:function(){n.each(function(){var n=a(this),e=n.css("background-image");e&&-1!=e.indexOf("data:image/svg+xml;base64")&&o.push(n)})},paint:function(){a.each(o,function(n,e){var a=e.parent().parent();a.hasClass("current")||a.hasClass("wp-has-current-submenu")?t.paintElement(e,"current"):(t.paintElement(e,"base"),a.on("mouseenter",function(){t.paintElement(e,"focus")}).on("mouseleave",function(){i.setTimeout(function(){t.paintElement(e,"base")},100)}))})},paintElement:function(n,e){var a,t;if(e&&s.hasOwnProperty(e)&&(e=s[e]).match(/^(#[0-9a-f]{3}|#[0-9a-f]{6})$/i)&&"none"!==(a=n.data("wp-ui-svg-"+e))){if(!a){if(!(t=n.css("background-image").match(/.+data:image\/svg\+xml;base64,([A-Za-z0-9\+\/\=]+)/))||!t[1])return void n.data("wp-ui-svg-"+e,"none");try{a=i.atob(t[1])}catch(n){}if(!a)return void n.data("wp-ui-svg-"+e,"none");a=(a=(a=a.replace(/fill="(.+?)"/g,'fill="'+e+'"')).replace(/style="(.+?)"/g,'style="fill:'+e+'"')).replace(/fill:.*?;/g,"fill: "+e+";"),a=i.btoa(a),n.data("wp-ui-svg-"+e,a)}n.attr("style",'background-image: url("data:image/svg+xml;base64,'+a+'") !important;')}}}}(jQuery,window,document);